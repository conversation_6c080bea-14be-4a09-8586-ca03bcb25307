'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';

export default function StepPersonalInfo() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Date of Birth
          </label>
          <Input type="date" {...register('dob')} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Nationality
          </label>
          <Input type="text" {...register('nationality')} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Ethnicity
          </label>
          <Input type="text" {...register('ethnicity')} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Gender</label>
          <Input type="text" {...register('gender')} />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Home Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Address
            </label>
            <Input {...register('home.address')} />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Telephone
            </label>
            <Input {...register('home.telephone')} />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Travel Time to Hospital
            </label>
            <Input {...register('home.travelTime')} />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Email Address
            </label>
            <Input type="email" {...register('home.email')} />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Next of Kin
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Name
            </label>
            <Input {...register('nextOfKin.name')} />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Telephone
            </label>
            <Input {...register('nextOfKin.telephone')} />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Relationship
            </label>
            <Input {...register('nextOfKin.relationship')} />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Private Practice Address (if not applicable, type Nill)
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Address
            </label>
            <Input {...register('practice.address')} />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Telephone
            </label>
            <Input {...register('practice.telephone')} />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Email
            </label>
            <Input type="email" {...register('practice.email')} />
          </div>
          <div className="space-y-2 md:col-span-2">
            <label className="text-sm font-medium text-foreground">
              Website
            </label>
            <Input {...register('practice.website')} />
          </div>
        </div>
      </div>
    </div>
  );
}
