'use client';

import { Paths } from '@/components/navigations/data';
import { Percent, Plus, TestTubeDiagonal } from 'lucide-react';
import Package from './components/package';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

export default function PackageContent() {
  const editPermission = hasPermission(PERMISSIONS.PACKAGE_EDIT);
  const createPermission = hasPermission(PERMISSIONS.PACKAGE_CREATE);

  return (
    <div className="container mx-auto space-y-4">
      <div className="flex flex-wrap justify-between">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <Percent className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
          Manage Packages
        </h2>
        <div>
          <div className="flex flex-wrap gap-2">
            {createPermission && (
              <>
                <Link href={`${Paths.Packages}/investigation`}>
                  <Button variant="outline" className="cursor-pointer">
                    <TestTubeDiagonal className="w-3.5 h-3.5" /> Investigations
                  </Button>
                </Link>
                <Link href={`${Paths.Packages}/new-package`}>
                  <Button className="cursor-pointer">
                    <Plus className="w-3.5 h-3.5" /> New package
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        <Package hasEditPermit={editPermission} />
      </div>
    </div>
  );
}
