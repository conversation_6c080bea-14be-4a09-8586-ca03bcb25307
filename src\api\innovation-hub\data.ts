import { myApi } from '../fetcher';
import { toast } from 'sonner';
import { useAuthSWR } from '../useAuthSWR';

// Types
export interface Idea {
  id: number;
  title: string;
  slug: string;
  description: string;
  author: {
    id: string;
    fullName: string;
  };
  createdAt: string;
  updatedAt: string;
  likes: any;
  comments: IdeaComment[];
  status: IdeaStatus;
  isLikedByUser?: boolean;
}

export interface IdeaComment {
  id: number;
  ideaId: number;
  author: {
    id: string;
    name: string;
  };
  content: string;
  createdAt: string;
}

export type IdeaStatus =
  | 'DRAFT'
  | 'PENDING_REVIEW'
  | 'ACCEPTED'
  | 'REJECTED'
  | 'IMPLEMENTED';

export interface IdeaFormValues {
  title: string;
  description: string;
  category?: string;
  tags?: string[];
}

export interface IdeasResponse {
  ideas: Idea[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const GetIdeaCategories = () => {
  const { data, isLoading, error, mutate } = useAuthSWR(
    '/innovation/idea-categories'
  );

  return {
    ideasCat: data?.data || [],
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };
};

export const GetIdeaTags = () => {
  const { data, isLoading, error, mutate } = useAuthSWR(
    '/innovation/idea-tags'
  );

  return {
    ideasTags: data?.data || [],
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };
};

export const GetIdeas = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useAuthSWR(
    `/innovation/idea/list?${qs.toString()}`
  );

  return {
    ideas: data?.data,
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };
};

// Like/Unlike an idea
export const toggleIdeaLike = async (ideaId: number) => {
  try {
    const res = await myApi.post(`/innovation/idea/${ideaId}/like`);
    toast.success(res.data.data.message);
    return;
  } catch (error) {
    console.error('Error toggling idea like:', error);
    throw error;
  }
};

// Update idea status (admin only)
export const updateIdeaStatus = async (ideaId: number, status: IdeaStatus) => {
  try {
    const response = await myApi.put(`/innovation/idea/update`, {
      ideaId,
      status,
    });
    toast.success(response.data.data.message);
    return response.data;
  } catch (error) {
    console.error('Error updating idea status:', error);
    throw error;
  }
};

// Add comment to an idea
export const addIdeaComment = async (ideaId: number, content: string) => {
  try {
    const res = await myApi.post(`/innovation/idea/comment`, {
      content,
      ideaId,
    });
    toast.success(res.data.data.message);
    return;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

// Delete an idea
export const deleteIdea = async (ideaId: number) => {
  try {
    const response = await myApi.delete(`/innovation/idea/${ideaId}`);
    toast.success(response.data.data.message);
    return response.data;
  } catch (error) {
    console.error('Error deleting idea:', error);
    throw error;
  }
};

// Get innovation hub statistics
export const GetIdeaStats = () => {
  const { data, error, isLoading, mutate } = useAuthSWR('/innovation/stats');

  return {
    stats: data?.data,
    statsLoading: isLoading,
    statsError: error,
    statsMutate: mutate,
  };
};

// Get leaderboard data
export const GetLeaderboard = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useAuthSWR(
    `/innovation/leaderboard?${qs.toString()}`
  );

  return {
    leaderboard: data?.data,
    leaderboardLoading: isLoading,
    leaderboardError: error,
    leaderboardMutate: mutate,
  };
};
