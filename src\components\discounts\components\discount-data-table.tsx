'use client';

import React, { useState, useEffect } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search } from 'lucide-react';
import { GetDiscounts } from '@/api/data';
import { numberFormat } from '@/lib/utils';
import Create from './create';
import DateRangeFilter from '@/components/common/date-range-filter';
import Details from './details';
import { Input } from '@/components/ui/input';

interface DiscountCodeProps {
  openCreate: boolean;
  // permitEdit: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

const DiscountCode: React.FC<DiscountCodeProps> = ({
  openCreate,
  //   permitEdit,
  setOpenCreate,
}) => {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [queryParam, setQueryParam] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [openDetails, setOpenDetails] = useState(false);
  const [detail, setDetail] = useState<any | null>(null);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Update query parameters when search term or date range changes
  useEffect(() => {
    let newQueryParam = '';

    if (debouncedSearchTerm) {
      newQueryParam = `search=${debouncedSearchTerm}`;
    }

    // Add date range parameters if they exist
    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      newQueryParam = newQueryParam
        ? `${newQueryParam}&startDate=${formattedStartDate}`
        : `startDate=${formattedStartDate}`;
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      newQueryParam = newQueryParam
        ? `${newQueryParam}&endDate=${formattedEndDate}`
        : `endDate=${formattedEndDate}`;
    }

    setCurrentPage(1); // Reset to first page on new search
    setQueryParam(newQueryParam);
  }, [debouncedSearchTerm, startDate, endDate]);

  const { discount, discountLoading, discountError, mutate } = GetDiscounts(
    `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );
  const data = discount?.data?.discounts;
  const totalPages = discount?.data?.totalPages ?? 0;
  const totalCount = discount?.data?.totalCount;

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  const handleEventFromModal = (discount: any) => {
    setDetail(discount);
    setOpenDetails(true);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="flex flex-wrap gap-3 items-center">
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search discount code..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
          </div>
        </div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black whitespace-nowrap">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date Created</th>
              <th className="table-style">Code</th>
              <th className="table-style">Start Date</th>
              <th className="table-style">End Date </th>
              <th className="table-style">Status</th>
              <th className="table-style">Value</th>
              <th className="table-style">Discounted No</th>
              <th className="table-style">Edit</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {discountLoading ? (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading discount code(s)...</span>
                  </div>
                </td>
              </tr>
            ) : data && data.length > 0 ? (
              data?.map((value: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={value.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dayjs(value.createdAt).format('MMMM D, YYYY')}
                  </td>
                  <td className="table-style">{value.modifierCode}</td>
                  <td className="table-style">
                    {value.startDate
                      ? dayjs(value.startDate).format('MMMM D, YYYY')
                      : '-'}
                  </td>
                  <td className="table-style">
                    {value.endDate
                      ? dayjs(value.endDate).format('MMMM D, YYYY')
                      : '-'}
                  </td>
                  <td className="table-style">
                    <span
                      className={
                        value.isActive
                          ? 'text-green-600 font-medium'
                          : 'text-gray-500 font-medium'
                      }
                    >
                      {value.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="table-style">
                    {value.percentage
                      ? `${value.percentage}%`
                      : numberFormat(value.amount)}
                  </td>
                  <td className="table-style">{value.packagePricesCount}</td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(value)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <p className="text-lg font-medium">
                      No discount codes found
                    </p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Discount codes will appear here once they are created'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
      {detail && openDetails && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          mutate={mutate}
          data={detail}
        />
      )}
    </>
  );
};

export default DiscountCode;
