'use client';

import React, { useState } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Ellipsis } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { GetRewards } from '@/api/reward/data';
import Create from './create';
import Details from './details';

interface RewardsProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

const RewardsTable: React.FC<RewardsProps> = ({
  openCreate,
  setOpenCreate,
}) => {
  const [open, setOpen] = useState(false);
  const { currentPage, pageSize, handlePageChange } = useSearchAndPagination({
    initialPageSize: 10,
  });

  const [detail, setDetail] = useState<any | null>(null);

  const { rewards, isLoading, mutate } = GetRewards();
  const rewardData = rewards?.data?.rewards;
  const totalPages = rewards?.totalPages;


  const handleEventFromModal = (reward: any) => {
    setDetail(reward);
    setOpen(true);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
 <table className="w-full whitespace-nowrap table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
                  <th className="table-style">S/N</th>
              <th className="table-style">Date Created</th>
              <th className="table-style">Value Type</th>
              <th className="table-style">Value</th>
              <th className="table-style">Awarded Count</th>
              <th className="table-style">Valid From</th>
              <th className="table-style">Valid Until</th>
              <th className="table-style">Status</th>
              <th className="table-style">Action</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {isLoading ? (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading reward...</span>
                  </div>
                </td>
              </tr>
            ) : rewardData && rewardData.length > 0 ? (
              rewardData?.map((reward: any, index: any) => (
               <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={reward.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">
                  {dayjs(reward.createdAt).format('MMMM D, YYYY')}
                </td>
                <td className="table-style">{reward.valueType}</td>
                <td className="table-style">{reward.value}</td>
                <td className="table-style">{reward.staffRewardCount}</td>
                  <td className="table-style">  {dayjs(reward.validFrom).format('MMMM D, YYYY')}</td>
                  <td className="table-style">  {dayjs(reward.validUntil).format('MMMM D, YYYY')}</td>
                <td className="table-style">
                  {reward.isActive
                    ? StatusBadge({ status: 'active' })
                    : StatusBadge({ status: 'inactive' })}
                </td>
                <td className="table-style">
                  <Ellipsis
                    onClick={() => handleEventFromModal(reward)}
                    className="w-4 h-4 cursor-pointer"
                  />
                </td>
              </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <Ellipsis className="h-8 w-8 mb-2 opacity-50" />
                    <p className="font-medium">No reward found</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </>
  );
};

export default RewardsTable;
