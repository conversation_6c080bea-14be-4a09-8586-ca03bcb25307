'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ArrowLeft } from 'lucide-react';
import { GetInventorySupplies } from '@/api/cafeteria/data';
import { InvoicePreview } from './invoice-preview';
import dayjs from 'dayjs';

interface ViewSuppliesProps {
  selectedItem: any;
  onBack: () => void;
}

export function ViewSupplies({ selectedItem, onBack }: ViewSuppliesProps) {
  const { suppliesData, suppliesLoading } = GetInventorySupplies(
    selectedItem.id
  );
  const supplyData = suppliesData?.data;

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Inventory
        </Button>
        <h1 className="text-lg font-bold">
          Supply History - {selectedItem?.itemName}
        </h1>
      </div>

      <div className="border rounded-md p-2">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Cost Per Unit</TableHead>
              <TableHead>Total Cost</TableHead>
              <TableHead>Invoice #</TableHead>
              <TableHead>Invoice</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {supplyData?.length > 0 ? (
              supplyData?.map((supply: any) => (
                <TableRow key={supply.id}>
                  <TableCell>
                    {dayjs(supply.date).format('YYYY-MM-DD')}
                  </TableCell>
                  <TableCell>{supply.supplier}</TableCell>
                  <TableCell>{supply.quantitySupplied}</TableCell>
                  <TableCell>${supply.costPerUnit}</TableCell>
                  <TableCell>{supply.totalCost}</TableCell>
                  <TableCell>{supply.invoiceNo}</TableCell>
                  <TableCell>
                    {supply.invoice && (
                      <InvoicePreview
                        invoicePath={`${process.env.NEXT_PUBLIC_CEDARCREST_API_BASE}/${supply.invoice}`}
                      />
                    )}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No supply history found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
