'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Download, Eye, Loader2 } from 'lucide-react';

interface InvoicePreviewProps {
  invoicePath: string;
}

export function InvoicePreview({ invoicePath }: InvoicePreviewProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(invoicePath);
  const isPdf = /\.pdf$/i.test(invoicePath);

  useEffect(() => {
    let objectUrl: string | null = null;

    if (isOpen && (isPdf || isImage)) {
      setIsLoading(true);
      setError(null);
      fetch(invoicePath)
        .then((res) => {
          if (!res.ok) {
            throw new Error('Failed to fetch file for preview');
          }
          return res.blob();
        })
        .then((blob) => {
          objectUrl = URL.createObjectURL(blob);
          setFileUrl(objectUrl);
        })
        .catch((err) => {
          console.error('Error fetching invoice:', err);
          setError('Could not load preview.');
        })
        .finally(() => {
          setIsLoading(false);
        });
    }

    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
        setFileUrl(null);
      }
    };
  }, [isOpen, isPdf, isImage, invoicePath]);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = fileUrl || invoicePath; // Use blob URL if available
    link.download = invoicePath.split('/').pop() || 'invoice';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2"
      >
        <Eye className="h-4 w-4" />
        View Invoice
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex gap-2">
              Invoice Preview{' '}
              <span>
                <Download
                  onClick={handleDownload}
                  className="h-5 w-5 text-gray-500 hover:text-gray-800 hover:cursor-pointer"
                />
              </span>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-auto flex justify-center items-center min-h-[70vh]">
            {isLoading && <Loader2 className="h-8 w-8 animate-spin" />}
            {error && <p className="text-destructive">{error}</p>}
            {!isLoading && !error && (
              <>
                {isImage && fileUrl && (
                  <img
                    src={fileUrl}
                    alt="Invoice"
                    className="w-full h-auto max-h-[70vh] object-contain"
                  />
                )}
                {isPdf && fileUrl && (
                  <iframe
                    src={fileUrl}
                    className="w-full h-[70vh]"
                    title="Invoice PDF"
                  />
                )}
                {!isImage && !isPdf && (
                  <div className="text-center py-8">
                    <p>Preview not available for this file type.</p>
                    <Button onClick={handleDownload} className="mt-4">
                      Download File
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
