'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Lightbulb, CheckCircle } from 'lucide-react';

export const StatsCards = ({ stats }: any) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Total Ideas
          </CardTitle>
          <div className={`p-2 rounded-lg bg-blue-100 dark:bg-blue-900`}>
            <Lightbulb className={`h-4 w-4 text-blue-600 dark:text-blue-400`} />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats?.total || 0}</div>
          <div className="flex flex-wrap gap-2 text-xs font-medium text-gray-400 dark:text-gray-600">
            <h3>Draft Ideas - {stats?.draft || 0}</h3>
            <h3>Pending Review - {stats?.pending_review || 0}</h3>
            <h3>Rejected Ideas - {stats?.rejected || 0}</h3>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Implemented Ideas
          </CardTitle>
          <div className={`p-2 rounded-lg bg-green-100 dark:bg-green-900`}>
            <CheckCircle className={`h-4 w-4 `} />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats?.implemented || 0}</div>
          <div className="flex flex-wrap gap-2 text-xs font-medium text-gray-400 dark:text-gray-600">
            <h3>Accepted Ideas - {stats?.accepted || 0}</h3>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
