# Menu Scheduler System

## Overview

A comprehensive menu scheduling system for inpatients that allows:

1. **Admin Menu Management**: Weekly menu scheduling for Breakfast, Lunch, and Dinner
2. **Patient Ordering**: Public interface for patients to schedule meals 24 hours in advance

## Features

### Admin Features (Authenticated)

- **Menu Scheduler Tab**: Located in Cafeteria Management
- **Weekly View**: Schedule menus for each day of the week
- **Meal Types**: Breakfast, Lunch, Dinner organization
- **Drag & Drop**: Easy menu assignment to time slots
- **Permission-based Access**: Requires `CAFETERIA_MENU_SCHEDULE` permission

### Patient Features (Public)

- **24-Hour Advance Ordering**: Patients can only order meals for dates at least 24 hours in the future
- **Patient ID Authentication**: Simple ID-based access
- **Visual Menu Selection**: Clear display of available meals by type
- **Order Summary**: Real-time total calculation
- **Responsive Design**: Mobile-friendly interface

## File Structure

### API Layer

- `src/api/cafeteria/scheduler.ts` - API functions for menu scheduling
- `src/lib/types/permissions.ts` - Added CAFETERIA_MENU_SCHEDULE permission

### Admin Pages

- `src/app/(authenticated)/cafeteria/menu-scheduler/page.tsx` - Admin scheduler page
- `src/components/cafeteria/scheduler/weekly-scheduler.tsx` - Weekly scheduling component
- `src/components/cafeteria/index.tsx` - Updated with scheduler tab

### Public Pages

- `src/app/patient-menu-order/page.tsx` - Public patient ordering page
- `src/app/patient-menu-order/layout.tsx` - Simple layout for public access
- `src/components/cafeteria/scheduler/patient-menu-selector.tsx` - Patient menu selection component

### Navigation

- `src/components/navigations/data.ts` - Added MenuScheduler path

## Usage

### For Administrators

1. Navigate to Cafeteria Management
2. Click on "Menu Scheduler" tab
3. Select the week using the week picker
4. Add menu items to each day and meal type
5. Save the schedule

### For Patients

1. Visit `/patient-menu-order` (public URL)
2. Enter Patient ID
3. Select date (minimum 24 hours in advance)
4. Choose meals from available options
5. Submit order

## Permissions Required

- `CAFETERIA_MENU_SCHEDULE` - For admin menu scheduling access
- `CAFETERIA_VIEW` - For general cafeteria access

## API Endpoints Expected

- `GET /cafeteria/menu/weekly?week=YYYY-MM-DD` - Get weekly menu schedule
- `POST /cafeteria/menu/weekly` - Save weekly menu schedule
- `POST /cafeteria/patient-orders` - Submit patient meal orders
- `GET /cafeteria/menu/list` - Get available menu items

## Dependencies

- All UI components are already available in the project
- Uses existing API structure and authentication
- Leverages current permission system
- Integrates with existing cafeteria management
