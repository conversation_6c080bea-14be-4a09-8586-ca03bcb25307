'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { GetProfile } from '@/api/staff';
import { Logout } from '@/lib/utils';
import { useAccountDeactivationModal } from './useAccountDeactivationModal';
import { mutate } from 'swr';

/**
 * Hook to verify if the user profile is loaded with proper roles and permissions
 * @param timeoutMs Optional timeout in milliseconds (default: 15000ms)
 * @returns An object with loading status, success status, and error information
 */
export function useProfileVerification(timeoutMs: number = 15000) {
  const { profile, isLoading, error } = GetProfile();
  const router = useRouter();
  const { showModal, handleLogout, checkAccountStatus } = useAccountDeactivationModal();
  const [hasTimedOut, setHasTimedOut] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [verificationState, setVerificationState] = useState({
    isLoading: true,
    isSuccess: false,
    isError: false,
    isTimeout: false,
    error: null as Error | null | unknown,
  });

  const permissions =
    profile?.data?.roles?.flatMap(
      (role: any) => role.permissions?.map((p: any) => p.action) || []
    ) || [];

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await mutate('/api/staff/profile');
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleProfileModalLogout = () => {
    setShowProfileModal(false);
    Logout();
    window.location.href = '/login';
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isLoading) {
      timer = setTimeout(() => {
        setHasTimedOut(true);
      }, timeoutMs);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isLoading, timeoutMs]);

  // useEffect(() => {
  //   console.log('useProfileVerification running', {
  //     profile,
  //     isLoading,
  //     error,
  //     hasTimedOut,
  //   });
  // }, [profile, isLoading, error, hasTimedOut]);

  useEffect(() => {
    if (hasTimedOut && isLoading) {
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: true,
        error: new Error(`Profile verification timed out after ${timeoutMs}ms`),
      });
      return;
    }

    if (isLoading && !hasTimedOut) {
      setVerificationState({
        isLoading: true,
        isSuccess: false,
        isError: false,
        isTimeout: false,
        error: null,
      });
      return;
    }

    if (error) {
      // Don't show modal for account deactivation errors
      if (!(error instanceof Error && error.message === 'ACCOUNT_DEACTIVATED')) {
        setShowProfileModal(true);
      }
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: false,
        error,
      });
      return;
    }

    // Profile is null (definitive negative result)
    if (profile === null) {
      setShowProfileModal(true);
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: false,
        error: new Error('Profile is null'),
      });
      return;
    }

    // Profile exists but missing roles
    const roles =
      profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

    //  const permissions = profile?.data?.permissions || [];

    if (profile && (!profile.data || !roles.length)) {
      setShowProfileModal(true);
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: false,
        error: new Error('Profile exists but roles are missing'),
      });
      return;
    }

    // Check if account is deactivated (isActive === false)
    if (profile?.data && profile.data.isActive === false) {
      console.log('[AUTH] Account is deactivated, showing modal and preventing further processing');
      checkAccountStatus(profile.data.isActive);
      // Set a special state that indicates deactivation
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: false,
        error: new Error('ACCOUNT_DEACTIVATED'),
      });
      return;
    }

    // Success case - profile exists with roles and is active
    setShowProfileModal(false);
    setVerificationState({
      isLoading: false,
      isSuccess: true,
      isError: false,
      isTimeout: false,
      error: null,
    });
  }, [profile, isLoading, error, hasTimedOut, timeoutMs, router]);

  return { 
    ...verificationState, 
    profile, 
    permissions, 
    showDeactivationModal: showModal, 
    handleDeactivationLogout: handleLogout,
    showProfileModal,
    isRetrying,
    handleRetry,
    handleProfileModalLogout
  };
}
