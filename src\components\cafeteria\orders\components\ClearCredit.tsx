'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Modal } from '@/components/common/modal';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';
import { GetProfile } from '@/api/staff';

interface PaymentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  unpaidAmount?: number;
  staffId?: string;
  staffName?: string;
  walletBalance?: number;
  mealVoucherBalance?: number;
  onPaymentSuccess?: () => void;
}

export default function PaymentModal({
  open,
  setOpen,
  staffId = '',
  onPaymentSuccess,
}: PaymentModalProps) {
  const [paymentMode, setPaymentMode] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [displayAmount, setDisplayAmount] = useState<string>('');
  const [inputStaffId, setInputStaffId] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [verifiedStaffName, setVerifiedStaffName] = useState<string>('');
  const [isVerifyingStaff, setIsVerifyingStaff] = useState(false);
  const [staffVerificationError, setStaffVerificationError] =
    useState<string>('');

  const { profile } = GetProfile();

  // Initialize staffId input when modal opens
  useEffect(() => {
    if (open && staffId) {
      setInputStaffId(staffId);
    }
  }, [open, staffId]);

  // Verify staff ID when it changes (only if 6+ characters)
  useEffect(() => {
    const verifyStaff = async () => {
      if (!inputStaffId.trim() || inputStaffId.trim().length < 6) {
        setVerifiedStaffName('');
        setStaffVerificationError('');
        return;
      }

      setIsVerifyingStaff(true);
      setStaffVerificationError('');

      try {
        const response = await myApi.post('/staff/verify', {
          staffCode: inputStaffId.trim(),
        });
        if (response.status === 200) {
          const staffName = response.data.data.name;
          setVerifiedStaffName(staffName);
        } else {
          setVerifiedStaffName('');
          setStaffVerificationError(response.data.data.message);
        }
      } catch (error: any) {
        setStaffVerificationError('Error verifying staff');
      } finally {
        setIsVerifyingStaff(false);
      }
    };

    const timeoutId = setTimeout(verifyStaff, 500); // Debounce API calls
    return () => clearTimeout(timeoutId);
  }, [inputStaffId]);

  const handleClose = () => {
    setOpen(false);
    setPaymentMode('');
    setAmount('');
    setDisplayAmount('');
    setInputStaffId('');
    setVerifiedStaffName('');
    setIsVerifyingStaff(false);
    setStaffVerificationError('');
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^\d.]/g, '');
    const parts = value.split('.');
    if (parts.length > 2) {
      return;
    }
    if (parts[1] && parts[1].length > 2) {
      return;
    }

    setAmount(value);

    // Format display value with commas
    if (value) {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        setDisplayAmount(
          new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          }).format(numValue)
        );
      } else {
        setDisplayAmount(value);
      }
    } else {
      setDisplayAmount('');
    }
  };

  const handlePayment = async () => {
    // Validation
    if (!inputStaffId.trim()) {
      toast.error('Please enter staff ID');
      return;
    }

    if (!verifiedStaffName && !staffVerificationError) {
      toast.error('Please wait for staff verification to complete');
      return;
    }

    if (staffVerificationError) {
      toast.error('Please enter a valid staff ID');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!paymentMode) {
      toast.error('Please select a payment mode');
      return;
    }

    try {
      setIsProcessing(true);

      const payload: any = {
        staffCode: inputStaffId.trim(),
        amount: parseFloat(amount),
        mode: paymentMode,
        creditedBy: profile?.data?.fullName,
      };

      const response = await myApi.post('/staff/pay-credit', payload);

      if (response.status === 200) {
        toast.success('Staff credited successfully');
        handleClose();
        if (onPaymentSuccess) {
          onPaymentSuccess();
        }
      }
    } catch (error) {
      console.error('Credit staff failed:', error);
      toast.error('Credit staff failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const paymentModes = [
    { value: 'deduction', label: 'Deduction' },
    { value: 'transfer', label: 'Transfer' },
    { value: 'card', label: 'Card' },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Credit Staff"
      description="Credit staff account with specified amount"
      onSubmit={handlePayment}
      isLoading={isProcessing}
    >
      <div className="grid gap-4 py-4">
        {/* Staff ID Input */}
        <div className="grid gap-2">
          <Label htmlFor="staffId">Staff ID</Label>
          <Input
            id="staffId"
            type="text"
            placeholder="Enter staff ID"
            value={inputStaffId}
            onChange={(e) => setInputStaffId(e.target.value)}
          />
          {/* Staff verification status */}
          <div className="min-h-[20px]">
            {isVerifyingStaff && (
              <p className="text-xs text-muted-foreground flex items-center gap-1">
                <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-900"></div>
                Verifying staff...
              </p>
            )}
            {verifiedStaffName && !isVerifyingStaff && (
              <p className="text-xs text-green-600 font-medium">
                ✓ {verifiedStaffName}
              </p>
            )}
            {staffVerificationError && !isVerifyingStaff && (
              <p className="text-xs text-red-600">✗ {staffVerificationError}</p>
            )}
          </div>
        </div>

        {/* Amount Input */}
        <div className="grid gap-2">
          <Label htmlFor="amount">Amount</Label>
          <Input
            id="amount"
            type="text"
            placeholder="Enter amount"
            value={displayAmount}
            onChange={handleAmountChange}
            disabled={!verifiedStaffName}
          />
        </div>

        {/* Payment Mode */}
        <div className="grid gap-2">
          <Label htmlFor="paymentMode">Payment Mode</Label>
          <Select
            value={paymentMode}
            onValueChange={setPaymentMode}
            disabled={!verifiedStaffName}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select payment mode" />
            </SelectTrigger>
            <SelectContent>
              {paymentModes.map((mode) => (
                <SelectItem key={mode.value} value={mode.value}>
                  {mode.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </Modal>
  );
}
