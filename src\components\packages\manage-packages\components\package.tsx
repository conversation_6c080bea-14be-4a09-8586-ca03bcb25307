'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { GetPackages } from '@/api/data';
import Image from 'next/image';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';
import Link from 'next/link';
import PackageImageEdit from '../../edit-package/components/package-image-edit';
import Pagination from '@/components/common/pagination';
import { EmptyState, LoadingState } from '@/components/common/dataState';

import {
  CheckCircle2,
  Ban,
  TicketCheck,
  Settings2,
  Loader,
} from 'lucide-react';

interface PackageProps {
  hasEditPermit: boolean;
}

export default function Package({ hasEditPermit }: PackageProps) {
  const [pageSize, setPageSize] = useState(12);
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingPackageId, setLoadingPackageId] = useState<string | null>(null);

  const { packages, packageLoading, mutate } = GetPackages(
    `?page=${currentPage}&limit=${pageSize}`
  );

  const packageData = packages?.data?.packages;
  const totalPages = packages?.data?.totalPages ?? 0;

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleEditStatus = async (id: string, status: boolean) => {
    try {
      setLoadingPackageId(id);
      const res = await myApi.patch('/package/update-package-status', {
        id: id,
        packageStatus: !status,
      });
      setLoadingPackageId(null);
      if (res.status === 200) {
        toast.success(res.data.message);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setLoadingPackageId(null);
    }
  };

  return (
    <div className="w-full">
      {packageLoading ? (
        <LoadingState />
      ) : packageData?.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          <div className="grid min-[450px]:grid-cols-2 min-[1300px]:grid-cols-3 gap-4 p-1">
            {packageData.map((item: any) => (
              <div
                key={item.id}
                className={cn(
                  'flex flex-col',
                  'w-full shrink-0',
                  'bg-white dark:bg-zinc-900/70',
                  'rounded-md',
                  'border border-zinc-100 dark:border-zinc-800',
                  'hover:border-zinc-200 dark:hover:border-zinc-700',
                  'transition-all duration-200',
                  'shadow-sm backdrop-blur-xl',
                  'flex-row'
                )}
              >
                <div className="hidden sm:block sm:w-24 md:w-30 h-full">
                  <Image
                    src={item.packageImage}
                    width={500}
                    height={500}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-4 space-y-3 flex-1">
                  <div className="flex items-start justify-between">
                    {hasEditPermit && (
                      <div className="gap-1 flex rounded-lg">
                        <PackageImageEdit
                          packageId={item.id}
                          currentImage={item.packageImage}
                          onImageUpdated={() => mutate && mutate()}
                        />
                        <Link href={`/packages/edit-package/${item.slug}`}>
                          <Settings2 className="edit-button" />
                        </Link>
                      </div>
                    )}

                    {loadingPackageId === item.id ? (
                      <div className="flex items-center justify-center w-20 h-6">
                        <Loader className="w-4 h-4 animate-spin" />
                      </div>
                    ) : (
                      <Badge
                        onClick={() =>
                          toast('Change Package Status', {
                            description:
                              "Click 'Change' to confirm, or ignore to cancel.",
                            action: {
                              label: 'Change',
                              onClick: () =>
                                handleEditStatus(item.id, item.packageStatus),
                            },
                          })
                        }
                        className={`${
                          item.packageStatus
                            ? 'text-emerald-600 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900/30'
                            : 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400'
                        } cursor-pointer`}
                      >
                        {item.packageStatus ? (
                          <>
                            <CheckCircle2 /> active
                          </>
                        ) : (
                          <>
                            <Ban /> Inactive
                          </>
                        )}
                      </Badge>
                    )}
                  </div>

                  <div>
                    <h3 className="font-semibold text-primary dark:text-zinc-100 mb-1 line-clamp-2">
                      {item.name}
                    </h3>
                    <p className="text-xs text-zinc-600 dark:text-zinc-400 line-clamp-2">
                      {item.category.name}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {item.packageLocationPrices.length > 0 &&
                      item.packageLocationPrices.map(
                        (locationPrice: any, index: number) => {
                          // Check if end date has passed
                          const endDate = new Date(locationPrice.endDate);
                          const today = new Date();
                          const isExpired = endDate < today;

                          return (
                            <Badge
                              variant={`${isExpired ? 'destructive' : 'default'}`}
                              className="bg-auto"
                              key={index}
                              title={
                                isExpired
                                  ? `Expired on ${endDate.toLocaleDateString()}`
                                  : `Valid until ${endDate.toLocaleDateString()}`
                              }
                            >
                              {locationPrice.location.name}
                              {isExpired && (
                                <span className="ml-1 text-xs">(Expired)</span>
                              )}
                            </Badge>
                          );
                        }
                      )}
                  </div>
                  <p className="text-xs text-zinc-600 dark:text-zinc-400 py-2">
                    Created by: {item.createdBy}
                  </p>
                  <div className="flex items-center text-xs text-zinc-600 dark:text-zinc-400">
                    <TicketCheck className="w-3.5 h-3.5 mr-1.5" />
                    <span>Total Booked: {item.packageBookings}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {totalPages > 1 && (
            <div className="mt-6">
              <Pagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
