'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';

export default function StepCover() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Cover Arrangements
          </label>
          <p className="text-xs text-muted-foreground">
            Please detail how you can demonstrate a support structure to provide safe cover and care
            for the patients, including anaesthesia services and support for patients returning to
            theatre:
          </p>
          <textarea
            {...register('cover.support')}
            className="flex min-h-[100px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Subcontracted third-party arrangements
          </label>
          <p className='text-xs text-muted-foreground'>Please detail if you want to deliver services via a subcontracted third-party arrangement,
            such as through an external company, hospital or clinic:</p>
          <textarea
            {...register('cover.subcontract')}
            className="flex min-h-[100px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
          />
        </div>
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Tax Registration Number
        </label>
        <Input {...register('cover.taxNo')} />
      </div>
    </div>
  );
}
