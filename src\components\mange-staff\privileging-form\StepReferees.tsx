'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function StepReferees() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <p className="text-sm text-muted-foreground">
        Please provide details for at least 2 professional referees who can
        support your
        application for practising privileges, one of whom must be your most recent employer
        and one must be from your Responsible Officer.
      </p>
      <p className="text-sm text-muted-foreground">Practitioners wishing to provide care for patients under the age of 16 must provide a
        reference from a Paediatric Surgeon / Paediatric Specialty Surgeon and Paediatric
        Anaesthetist in the case of Anaesthetic practising privileges, please provide the names
        and addresses of these referees as applicable.</p>

      {[1, 2, 3, 4].map((num) => (
        <Card key={num}>
          <CardHeader>
            <CardTitle className="text-base">Referee {num}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Full Name
                </label>
                <Input {...register(`referees.${num}.name`)} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Position/Title
                </label>
                <Input {...register(`referees.${num}.position`)} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Telephone
                </label>
                <Input {...register(`referees.${num}.telephone`)} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Email
                </label>
                <Input type="email" {...register(`referees.${num}.email`)} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Address
                </label>
                <Input {...register(`referees.${num}.address`)} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Relationship to Applicant
                </label>
                <Input {...register(`referees.${num}.relationship`)} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
