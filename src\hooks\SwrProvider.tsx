'use client';
import { SWRConfig } from 'swr';
import { ReactNode } from 'react';
import { fetcher } from '@/api/fetcher';

export const SWRProvider = ({ children }: { children: ReactNode }) => {
  return (
    <SWRConfig
      value={{
        fetcher,
        revalidateOnFocus: true,
        revalidateOnReconnect: true,
        // Global error handling
        shouldRetryOnError: (error) => {
          // Don't retry on network errors or when offline
          if (error?.isNetworkError || !navigator.onLine) {
            return false;
          }
          // Don't retry on 4xx errors (client errors)
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          return true;
        },
        errorRetryInterval: navigator.onLine ? 5000 : 30000,
        errorRetryCount: navigator.onLine ? 3 : 0,
      }}
    >
      {children}
    </SWRConfig>
  );
};
