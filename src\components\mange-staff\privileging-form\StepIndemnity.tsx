'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export default function StepIndemnity() {
  const { register, watch, setValue } = useFormContext();
  const isMember = watch('indemnity.member');

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Medical Indemnity
        </h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Are you a member of a medical defence organisation?
            </label>
            <Select onValueChange={(value) => setValue('indemnity.member', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Yes">Yes</SelectItem>
                <SelectItem value="No">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {isMember === 'Yes' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Organisation Name
                </label>
                <Input {...register('indemnity.organisation')} />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Indemnity Reference / Certificate No
                </label>
                <Input {...register('indemnity.reference')} />
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Declarations
        </h3>
        <p className="text-sm text-muted-foreground">
          Please answer YES/NO and If YES, provide details.
        </p>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Do you have any financial interest in any
              private healthcare company, or do you
              provide services to any other private
              healthcare company
            </label>
            <textarea
              {...register('declarations.financialInterests')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Do you have any other declarations
              /conflicts of interest to declare?
            </label>
            <textarea
              {...register('declarations.otherDeclarations')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Please state whether or not you are currently
              the subject of any police investigation in
              Nigeria or elsewhere.
            </label>
            <textarea
              {...register('declarations.police')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Have you ever been convicted of a criminal offence
              within Nigeria or elsewhere, or received a Police
              Caution?
            </label>
            <textarea
              {...register('declarations.conviction')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Are you currently the subject of any investigation
              or proceedings by any regulatory or professional
              body in the Nigeria or elsewhere?
            </label>
            <textarea
              {...register('declarations.regulatory')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Do you currently have or have you ever had any
              conditions, restrictions or suspension (including
              those put in place by an Interim Orders Tribunal)
              made to your practice by the MDCN, GDC or
              HCPC?
            </label>
            <textarea
              {...register('declarations.conditions')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Have you ever been disqualified from practice of a
              profession or been the subject of specific limitations
              on your practice following an investigation by a
              regulatory or professional body in the Nigeria or
              elsewhere?
            </label>
            <textarea
              {...register('declarations.disqualification')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Confirmation
        </h3>
        <p className='text-sm text-muted-foreground'>I hereby confirm that the details provided on this form are true and correct.
          <br />
          <br />I hereby give permission for Cedarcrest Healthcare Group to consult directly with the Medical and Dental
          Council of Nigeria (MDCN) to confirm any aspect of my medical indemnification on an ongoing basis until
          further notice.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Signature (Image Upload)
            </label>
            <Input
              type="file"
              accept="image/*"
              {...register('signature', { required: true })}
              className="block w-full text-sm text-gray-900 border border-gray-300 rounded-sm cursor-pointer bg-gray-50 focus:outline-none"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Initials
            </label>
            <Input {...register('shortSignature')} />
          </div>
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Date</label>
          <Input type="date" {...register('signatureDate')} />
        </div>
      </div>
    </div>
  );
}
