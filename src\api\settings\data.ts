import { useAuthSWR } from '../useAuthSWR';

// Get system settings
export const GetSystemSettings = () => {
  const { data, error, isLoading, mutate } = useAuthSWR(`/admin/system-settings`);

  return {
    settings: data,
    settingsLoading: isLoading,
    settingsError: error,
    mutate: mutate,
  };
};

// Get notification emails
export const GetNotificationEmails = () => {
  const { data, error, isLoading, mutate } = useAuthSWR(
    `/settings/admin-notification-emails`
  );

  return {
    notificationEmails: data,
    notificationEmailsLoading: isLoading,
    notificationEmailsError: error,
    mutate: mutate,
  };
};



//Suggestions
export const GetSuggestions = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useAuthSWR(
    `/suggestions/list?${qs.toString()}`
  );

  return {
    suggestions: data?.data,
    isLoading: isLoading,
    leaderboardError: error,
    mutate: mutate,
  };
};