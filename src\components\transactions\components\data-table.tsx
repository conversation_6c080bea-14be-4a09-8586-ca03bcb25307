'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, CreditCard, Download } from 'lucide-react';
import {
  GetTransactions,
  GetStaffTransactions,
  exportTransactionsToCSV,
} from '@/api/data';
import { numberFormat } from '@/lib/utils';
import { StatusBadge } from '@/components/common/status-badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import Details from './details';
import DateRangeFilter from '@/components/common/date-range-filter';
import MealVoucherModal from '../../rewards/components/meal-voucher-modal';
import { toast } from 'sonner';

const TransactionData = () => {
  const [open, setOpen] = useState(false);
  const [viewType, setViewType] = useState<'my' | 'all'>('my');
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [singleDate, setSingleDate] = useState<Date | undefined>(undefined);
  const [showMealVoucherModal, setShowMealVoucherModal] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const canEditTransactions = hasPermission(PERMISSIONS.TRANSACTION_EDIT);

  // Update query parameters when search term, type filter, status filter, or date range changes
  useEffect(() => {
    const params = buildQueryParams();
    const queryString = new URLSearchParams(params).toString();
    setQueryParam(queryString);
  }, [
    debouncedSearchTerm,
    typeFilter,
    statusFilter,
    startDate,
    endDate,
    singleDate,
    setQueryParam,
  ]);

  // Conditionally use different APIs based on viewType and permissions
  const {
    transactions: myTransactions,
    transactionLoading: myTransactionLoading,
  } = GetStaffTransactions(
    `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );
  const {
    transactions: allTransactions,
    transactionLoading: allTransactionLoading,
    mutate: allTransactionMutate,
  } = canEditTransactions
    ? GetTransactions(`?page=${currentPage}&limit=${pageSize}&${queryParam}`)
    : { transactions: null, transactionLoading: false };

  // Use appropriate data based on viewType
  const transactions = viewType === 'all' ? allTransactions : myTransactions;
  const transactionLoading =
    viewType === 'all' ? allTransactionLoading : myTransactionLoading;
  const data = transactions?.data?.transactions;
  const totalPages = transactions?.data?.totalPages ?? 0;

  // Check for pending transactions in all transactions
  const hasPendingTransactions =
    canEditTransactions &&
    allTransactions?.data?.transactions?.some(
      (transaction: any) => transaction.status.toLowerCase() === 'pending'
    );

  const handleEventFromModal = (transaction: any) => {
    setDetail(transaction);
    setOpen(true);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

  // Helper function to build query parameters
  const buildQueryParams = () => {
    const params: Record<string, any> = {};

    if (debouncedSearchTerm) {
      params.search = debouncedSearchTerm;
    }

    if (typeFilter && typeFilter !== 'all') {
      params.type = typeFilter;
    }

    if (statusFilter && statusFilter !== 'all') {
      params.status = statusFilter;
    }

    if (startDate) {
      params.startDate = dayjs(startDate).format('YYYY-MM-DD');
    }

    if (endDate) {
      params.endDate = dayjs(endDate).format('YYYY-MM-DD');
    }

    if (singleDate) {
      params.singleDate = dayjs(singleDate).format('YYYY-MM-DD');
    }

    return params;
  };

  // Export transactions to CSV
  const handleExportTransactions = async () => {
    try {
      setIsExporting(true);

      // Use the same parameter building logic as the useEffect
      const params = buildQueryParams();

      // Call the export API function
      const blob = await exportTransactionsToCSV(viewType, params);

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename with current date
      const currentDate = dayjs().format('YYYY-MM-DD');
      const filename = `transactions-${viewType}-${currentDate}.csv`;
      link.download = filename;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Transactions exported successfully');
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Export Failed');
    } finally {
      setIsExporting(false);
    }
  };

  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'SUCCESS', label: 'Success' },
    { value: 'FAILED', label: 'Failed' },
    { value: 'PROCESSING', label: 'Processing' },
    { value: 'CONFIRMED', label: 'Confirmed' },
    { value: 'CANCELLED', label: 'Cancelled' },
  ];

  const buttonOptions = [
    { label: 'All', param: '', className: '' },
    {
      label: 'Package',
      param: 'package',
      className:
        'bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400',
    },
    {
      label: 'Transfer',
      param: 'transfer',
      className:
        'bg-teal-100 text-teal-600 hover:bg-teal-200 dark:bg-teal-900/30 dark:text-blue-teal',
    },
    {
      label: 'Withdrawal',
      param: 'withdrawal',
      className:
        'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400',
    },
    {
      label: 'Cafeteria',
      param: 'cafeteria',
      className:
        'bg-orange-100 text-orange-600 hover:bg-orange-200 dark:bg-orange-900/30 dark:text-orange-400',
    },
    {
      label: 'Reward',
      param: 'reward',
      className:
        'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400',
    },
    {
      label: 'Refund',
      param: 'refund',
      className:
        'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400',
    },
  ];

  const handleSelect = (param: string) => {
    setTypeFilter(param);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
    if (start || end) {
      setSingleDate(undefined);
    }
  };

  const handleSingleDateChange = (date: Date | null) => {
    setSingleDate(date || undefined);
    if (date) {
      setStartDate(undefined);
      setEndDate(undefined);
    }
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="space-x-2 space-y-4">
            {buttonOptions.map(({ label, param, className }) => (
              <StatusBadge
                status={label.toLowerCase()}
                className={`cursor-pointer ${className} ${typeFilter === param ? 'ring-2 ring-offset-1' : ''}`}
                key={label}
                onClick={() => handleSelect(param)}
              />
            ))}
          </div>
          <div className="flex flex-wrap gap-3 items-center">
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              onSingleDateChange={handleSingleDateChange}
              singleDate={singleDate}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search transactions..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
              {canEditTransactions && (
            <div className="flex gap-2">
              <Button
                variant={viewType === 'my' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewType('my')}
              >
                My Transactions
              </Button>
                <Button
                  variant={viewType === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewType('all')}
                  className={`relative ${hasPendingTransactions && viewType !== 'all' ? 'after:absolute after:top-1 after:right-1 after:w-2 after:h-2 after:bg-yellow-500 after:rounded-full' : ''}`}
                >
                  All Transactions
                </Button>
             
              <Button
                className="cursor-pointer"
                variant="outline"
                size="sm"
                onClick={handleExportTransactions}
                disabled={isExporting}
              >
                {isExporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </>
                )}
              </Button>
            </div>
               )}
          </div>
        </div>
        <table className="whitespace-nowrap w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Transaction Date</th>
              {/* <th className="table-style">Package Amount</th> */}
              <th className="table-style">Amount</th>
              <th className="table-style">Reference</th>
              <th className="table-style">Mode</th>
              <th className="table-style">Type</th>
              <th className="table-style">Status</th>
              <th className="table-style">More</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {transactionLoading ? (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading transactions...</span>
                  </div>
                </td>
              </tr>
            ) : data && data.length > 0 ? (
              data.map((transaction: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={transaction.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dayjs(transaction.createdAt).format('MMMM D, YYYY')}
                  </td>
                  <td className="table-style">
                    {numberFormat(transaction.amount)}
                  </td>
                  <td className="table-style">{transaction.reference}</td>
                  <td className="table-style capitalize">{transaction.mode}</td>
                  <td className="table-style">
                    <span className="font-semibold px-2 py-1 rounded-full text-[11px] bg-gray-100 text-gray-600">
                      {transaction.type}
                    </span>
                  </td>
                  <td className="table-style">
                    {StatusBadge({
                      status: transaction.status.toLowerCase(),
                    })}
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(transaction)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <CreditCard className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No transactions found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate || singleDate
                        ? 'Try adjusting your search or date filters'
                        : 'Transactions will appear here once they are created'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      {open && (
        <Details
          open={open}
          setOpen={setOpen}
          mutate={allTransactionMutate}
          data={detail}
        />
      )}
      {showMealVoucherModal && (
        <MealVoucherModal
          open={showMealVoucherModal}
          setOpen={setShowMealVoucherModal}
          mutate={allTransactionMutate}
        />
      )}
    </>
  );
};

export default TransactionData;
