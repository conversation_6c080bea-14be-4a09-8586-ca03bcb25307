'use client';

import React, { useState, useEffect } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Ellipsis, Search, Building } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import { GetDepartments } from '@/api/crm/data';
import Details from './details';
import Create from './create';
import { useDebounce } from '@/hooks/useDebounce';

interface DepartmentTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function DepartmentTable({
  openCreate,
  setOpenCreate,
}: DepartmentTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<any>(null);
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const handleEventFromModal = (department: any) => {
    setSelectedDepartment(department);
    setOpenDetails(true);
  };

  const { departments, departmentsLoading, mutate } = GetDepartments();

  const departmentData = departments?.data?.departments || [];

  // Filter departments based on search term
  const filteredDepartments = departmentData.filter((department: any) => {
    if (!debouncedSearchTerm) return true;

    const searchTermLower = debouncedSearchTerm.toLowerCase();
    return (
      department.name.toLowerCase().includes(searchTermLower) ||
      department.description.toLowerCase().includes(searchTermLower)
    );
  });

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search departments..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {departmentsLoading ? (
        <LoadingState />
      ) : filteredDepartments.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
                <th className="table-style">#</th>
                <th className="table-style">Department Name</th>
                <th className="table-style">Head of Department</th>
                <th className="table-style">Doctors</th>
                <th className="table-style">Status</th>
                <th className="table-style">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
              {filteredDepartments.map((department: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={department.id}
                >
                  <td className="table-style">{index + 1}</td>
                  <td className="table-style">
                    <div className="flex items-center gap-2">
                      <Building className="w-4 h-4 text-gray-500" />
                      <span>{department.name}</span>
                    </div>
                  </td>
                  <td className="table-style">
                    {department.headOfDepartment || 'Not assigned'}
                  </td>
                  <td className="table-style">
                    {department.doctorsCount || 0}
                  </td>
                  <td className="table-style">
                    <StatusBadge
                      status={department.isActive ? 'active' : 'inactive'}
                    />
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(department)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <EmptyState message="No departments found" />
      )}

      {selectedDepartment && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedDepartment}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
