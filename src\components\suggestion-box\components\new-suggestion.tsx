import React, { useState } from 'react';
import { InputTextArea } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '@/components/types';
import { z } from 'zod';

const suggestionSchema = z.object({
  anonymous: z.boolean(),
  content: z.string().min(10, 'Suggestion is too short').max(500, 'Suggestion is too long, make it brief'),
});

type SuggestionFormValues = z.infer<typeof suggestionSchema>;

const NewSuggestion: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<SuggestionFormValues>({
    resolver: zod<PERSON>esolver(suggestionSchema),
    defaultValues: {
      anonymous: false,
      content: '',
    },
  });

  const onSubmit = async (data: SuggestionFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/suggestions/create', data);
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Share Your Suggestion"
      description="Submit your suggestions, thoughts or concerns."
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-6">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...form.register('anonymous')}
              id="anonymous"
              className="rounded border-gray-300"
            />
            <label htmlFor="anonymous" className="text-sm font-medium">
              Submit anonymously
            </label>
          </div>
          <InputTextArea
            control={form.control}
            name="content"
            label="Suggestion"
            placeholder="Share your suggestion..."
          />
        </form>
      </Form>
    </Modal>
  );
};

export default NewSuggestion;
