'use client';

import React, { useState } from 'react';
import DateRangeFilter from '@/components/common/date-range-filter';

export default function TestDateFilterPage() {
  const [singleDate, setSingleDate] = useState<Date | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">DateRangeFilter Test Page</h1>
      
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Single Date Mode</h2>
        <div className="w-64">
          <DateRangeFilter
            defaultMode="single"
            allowModeSwitch={true}
            onSingleDateChange={setSingleDate}
            singleDate={singleDate}
          />
        </div>
        <p className="text-sm text-gray-600">
          Selected single date: {singleDate ? singleDate.toDateString() : 'None'}
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Range Mode</h2>
        <div className="w-64">
          <DateRangeFilter
            defaultMode="range"
            allowModeSwitch={true}
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onClear={() => {
              setStartDate(null);
              setEndDate(null);
            }}
          />
        </div>
        <p className="text-sm text-gray-600">
          Selected range: {startDate ? startDate.toDateString() : 'None'} - {endDate ? endDate.toDateString() : 'None'}
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Mode Switching Enabled</h2>
        <div className="w-64">
          <DateRangeFilter
            defaultMode="range"
            allowModeSwitch={true}
            onDateRangeChange={(start, end) => {
              console.log('Range changed:', start, end);
            }}
          />
        </div>
      </div>
    </div>
  );
}
