'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export default function StepConsultancy() {
  const { register, watch, setValue } = useFormContext();
  const currentGovtPost = watch('consultancy.currentGovtPost');

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Date of entry on Specialist Register
        </label>
        <Input
          type="date"
          {...register('consultancy.specialistRegisterDate')}
        />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Do you currently hold a consultant post in a Government Hospital?
        </label>
        <Select onValueChange={(value) => setValue('consultancy.currentGovtPost', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Yes">Yes</SelectItem>
            <SelectItem value="No">No</SelectItem>
          </SelectContent>
        </Select>
      </div>
      {currentGovtPost === 'Yes' && (
        <>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Hospital Name
            </label>
            <Input
              {...register('consultancy.currentGovtHospital')}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              How many clinical sessions are attached to your current job plan?
            </label>
            <Input
              type="number"
              {...register('consultancy.clinicalSessions')}
            />
          </div>
        </>
      )}

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Previous Employment
        </h3>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Have you ever held a substantive
            Government Hospital consultant appointment? Name the hospital
          </label>
          <Input
            {...register('consultancy.prevGovtHospital')}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Date of leaving post
          </label>
          <Input
            type="date"
            {...register('consultancy.prevGovtLeaveDate')}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Reason for leaving
          </label>
          <Input
            {...register('consultancy.reasonForLeaving')}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Locum Consultant
        </h3>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Have you ever held a locum consultant position? Name Hospital where locum consultant position based

          </label>
          <Input
            {...register('consultancy.locumHospital')}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Dates Post Held
          </label>
          <Input
            {...register('consultancy.locumDates')}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Additional Information
        </h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              If there are any gaps in your CV, please detail the reasons for these here
            </label>
            <textarea
              {...register('consultancy.gaps')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              If you have previously been employed in a position where your duties involved work with children or
              vulnerable adults, please detail the reason why that position ended

            </label>
            <textarea
              {...register('consultancy.childrenWork')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
