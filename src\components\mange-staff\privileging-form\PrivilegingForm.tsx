'use client';

import { useEffect, useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, ArrowLeft, Save, Send, CheckCircle, Download } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { GetProfile } from '@/api/staff';
import StepPersonalInfo from './StepPersonalInfo';
import StepConsultancy from './StepConsultancy';
import StepPrivileges from './StepPrivileges';
import StepReferees from './StepReferees';
import StepAdditionalInfo from './StepAdditionalInfo';
import StepCover from './StepCover';
import StepIndemnity from './StepIndemnity';
import { toast } from 'sonner';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const STORAGE_KEY = 'privileging-form-data';
const AUTO_SAVE_INTERVAL = 30000; // Auto-save every 30 seconds

const sections = [
  { title: 'Personal Information', component: StepPersonalInfo },
  { title: 'Category of Practising Privileges', component: StepPrivileges },
  { title: 'Consultancy Details', component: StepConsultancy },
  { title: 'Additional Information', component: StepAdditionalInfo },
  { title: 'Cover & Subcontracting', component: StepCover },
  { title: 'Referees', component: StepReferees },
  { title: 'Indemnity & Declarations', component: StepIndemnity },
];

export default function PrivilegingForm() {
  const [pdfGenerated, setPdfGenerated] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [pdfSize, setPdfSize] = useState<string | null>(null);

  const methods = useForm({
    defaultValues: typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}')
      : {}
  });

  const router = useRouter();
  const { profile: user } = GetProfile();
  const watchAllFields = methods.watch();

  // Load saved form data
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        Object.keys(parsedData).forEach(key => {
          methods.setValue(key, parsedData[key]);
        });
        setLastSaved(new Date().toLocaleTimeString());
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, [methods]);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      const formData = methods.getValues();
      localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
      setLastSaved(new Date().toLocaleTimeString());
    }, AUTO_SAVE_INTERVAL);

    return () => clearInterval(autoSaveInterval);
  }, [methods]);

  // Check if user is consultant
  useEffect(() => {
    if (user?.data && !user.data.doctorProfile?.isConsultant) {
      router.push('/forbidden');
    }
  }, [user, router]);

  const saveFormData = () => {
    const formData = methods.getValues();
    localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
    setLastSaved(new Date().toLocaleTimeString());
  };

  const handleSave = () => {
    setSaving(true);
    saveFormData();
    toast.success("Your progress has been saved. You can continue later.");
    setSaving(false);
  };

  const generatePDF = (data: any) => {
    try {
      toast.info("Generating PDF...");
      
      // Create a new PDF document with explicit unit specification
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      // Basic PDF with minimal content to test functionality
      doc.setFontSize(16);
      doc.text('PRACTISING PRIVILEGES APPLICATION', 105, 20, { align: 'center' });
      doc.setFontSize(12);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);
      doc.text('Application Form Data', 20, 40);
      
      // Generate PDF blob
      const pdfBlob = doc.output('blob');
      
      // Create a URL for the blob
      const pdfUrl = URL.createObjectURL(pdfBlob);
      setPdfUrl(pdfUrl);
      
      // Calculate file size
      const fileSizeInKB = Math.round(pdfBlob.size / 1024);
      const fileSizeText = `${fileSizeInKB} KB`;
      setPdfSize(fileSizeText);
      
      setPdfGenerated(true);
      toast.success("Your application has been converted to PDF. You can now download or submit it.");
      
      // Save the final form data
      saveFormData();
    } catch (error: any) {
      console.error('Error generating PDF:', error);
      toast.error(`PDF generation failed: ${error.message || "Unknown error"}`);
      setPdfGenerated(false);
      setPdfUrl(null);
      setPdfSize(null);
    }
  };

  const downloadPDF = () => {
    if (!pdfUrl) {
      toast.error("PDF not available. Please generate the PDF first.");
      return;
    }

    toast.success("Your PDF is being downloaded...");

    try {
      // Create a download link
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `privileging-application-${Date.now()}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Your PDF has been downloaded successfully.");
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error("Failed to download PDF. Please try again.");
    }
  };

  const submitApplication = () => {
    // Here you would send the PDF to the server
    toast("Your privileging application has been submitted successfully.");
    router.push('/dashboard');
  };

  if (!user?.data?.doctorProfile?.isConsultant) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => window.history.back()}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold text-foreground">
            Privileging Application Form
          </h1>
          <p className="text-muted-foreground mt-2">
            Complete your privileging application
            {lastSaved && (
              <span className="ml-2 text-sm text-muted-foreground">
                (Last saved: {lastSaved})
              </span>
            )}
          </p>
        </div>

        <FormProvider {...methods}>
          <form className="space-y-8">
            {/* Form Sections */}
            {sections.map((section, index) => {
              const SectionComponent = section.component;
              return (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle>{section.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <SectionComponent />
                  </CardContent>
                </Card>
              );
            })}

            {/* Action Buttons */}
            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <>Saving...</>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save Progress
                  </>
                )}
              </Button>

              <div className="flex space-x-4">
                {pdfGenerated ? (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={downloadPDF}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download PDF {pdfSize && `(${pdfSize})`}
                    </Button>
                    <Button
                      type="button"
                      onClick={submitApplication}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Send className="w-4 h-4 mr-2" />
                      Submit Application
                    </Button>
                  </>
                ) : (
                  <Button
                    type="button"
                    size="lg"
                    onClick={() => generatePDF(methods.getValues())}
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Convert to PDF
                  </Button>
                )}
              </div>
            </div>

            {pdfGenerated && (
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800 flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
                <p className="text-green-800 dark:text-green-300">
                  PDF has been generated successfully. You can now download or submit your application.
                </p>
              </div>
            )}
          </form>
        </FormProvider>
      </div>
    </div>
  );
}
