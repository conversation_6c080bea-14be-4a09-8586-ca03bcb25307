import { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertTriangle, CreditCard, Wallet } from 'lucide-react';
import { currencyFormat } from '@/lib/utils';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

interface ClearCreditModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  outstandingCredit: number;
  mealVoucherBalance: number;
  walletBalance: number;
  setProfileOpen?: (open: boolean) => void;
}

export const ClearCreditModal = ({
  open,
  setOpen,
  outstandingCredit,
  mealVoucherBalance,
  walletBalance,
  setProfileOpen,
}: ClearCreditModalProps) => {
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [paymentType, setPaymentType] = useState<'full' | 'partial'>('full');

  const paymentAmount =
    paymentType === 'full' ? outstandingCredit : outstandingCredit / 2;
  const canPayWithMealVoucher = mealVoucherBalance >= paymentAmount;
  const hasWalletBalance = walletBalance > 0;

  const handleClearCredit = async () => {
    if (!password) {
      toast.error('Please enter your password');
      return;
    }

    setIsLoading(true);
    const payload = {
      amount: paymentAmount,
      paymentType: paymentType,
      password: password,
    };

    try {
      const res = await myApi.post('/transaction/clear-credit', payload);
      if (res.status === 200) {
        toast.success('Credit cleared successfully');
        setIsLoading(false);
        setOpen(false);
        setProfileOpen?.(false);
        setPassword('');
      }
    } catch (error) {
      console.log(error);
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Clear Credit"
      description="Clear your outstanding credit balance"
      size="md"
    >
      <div className="space-y-4">
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
          <div className="flex items-center gap-2 mb-2">
            <CreditCard className="h-5 w-5 text-orange-600" />
            <span className="font-medium text-orange-800">
              Outstanding Credit
            </span>
          </div>
          <p className="text-lg font-semibold text-orange-700">
            {currencyFormat(outstandingCredit)}
          </p>
        </div>

        <div className="space-y-3">
          <label className="text-sm font-medium">Payment Type</label>
          <div className="space-y-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="paymentType"
                checked={paymentType === 'full'}
                onChange={() => setPaymentType('full')}
                className="w-4 h-4 text-blue-600"
              />
              <span className="text-sm">
                Full Payment - {currencyFormat(outstandingCredit)}
              </span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="paymentType"
                checked={paymentType === 'partial'}
                onChange={() => setPaymentType('partial')}
                className="w-4 h-4 text-blue-600"
              />
              <span className="text-sm">
                Partial Payment - {currencyFormat(outstandingCredit / 2)}
              </span>
            </label>
          </div>
        </div>

        {canPayWithMealVoucher ? (
          <>
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-2">
                <Wallet className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">
                  Meal Voucher Balance
                </span>
              </div>
              <p className="text-lg font-semibold text-green-700">
                {currencyFormat(mealVoucherBalance)}
              </p>
              <p className="text-sm text-green-600 mt-1">
                ✓ Sufficient balance for {paymentType} payment
              </p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                Enter Password to Confirm
              </label>
              <Input
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleClearCredit}
                disabled={!password.trim() || isLoading}
                className="flex-1"
              >
                {paymentType === 'full'
                  ? 'Clear Full Credit'
                  : 'Make Partial Payment'}
              </Button>
            </div>
          </>
        ) : hasWalletBalance ? (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="space-y-2">
                <p className="font-medium text-blue-800">
                  Insufficient Meal Voucher Balance
                </p>
                <p className="text-sm text-blue-700">
                  Your meal voucher balance (
                  {currencyFormat(mealVoucherBalance)}) is insufficient for the
                  selected payment amount ({currencyFormat(paymentAmount)}).
                </p>
                <p className="text-sm text-blue-700">
                  Please top up your meal voucher from your wallet balance (
                  {currencyFormat(walletBalance)}) and return to complete
                  clearing your credit.
                </p>
              </div>
            </div>
            <div className="flex gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                className="flex-1"
              >
                Close
              </Button>
            </div>
          </div>
        ) : (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
              <div className="space-y-2">
                <p className="font-medium text-red-800">
                  Unable to Clear Credit
                </p>
                <p className="text-sm text-red-700">
                  You don't have sufficient meal voucher balance (
                  {currencyFormat(mealVoucherBalance)}) or wallet balance for
                  the selected payment amount ({currencyFormat(paymentAmount)}).
                </p>
                <p className="text-sm text-red-700">
                  Please visit the cafeteria admin for guidance on clearing your
                  credit balance.
                </p>
              </div>
            </div>
            <div className="flex gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                className="flex-1"
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};
