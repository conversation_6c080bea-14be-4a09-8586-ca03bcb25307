import { useState, useEffect } from 'react';

interface UseSearchAndPaginationProps {
  initialPageSize?: number;
  debounceDelay?: number;
}

interface UseSearchAndPaginationReturn {
  // Search state
  searchTerm: string;
  debouncedSearchTerm: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;

  // Pagination state
  currentPage: number;
  pageSize: number;
  handlePageChange: (pageNumber: number) => void;

  // Query building
  queryParam: string;
  setQueryParam: (param: string) => void;
}

export function useSearchAndPagination({
  initialPageSize = 15,
  debounceDelay = 500,
}: UseSearchAndPaginationProps = {}): UseSearchAndPaginationReturn {
  const [pageSize] = useState(initialPageSize);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [queryParam, setQueryParam] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceDelay);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceDelay]);

  // Update query parameters when search term changes
  useEffect(() => {
    let newQueryParam = '';

    if (debouncedSearchTerm) {
      newQueryParam = `search=${debouncedSearchTerm}`;
    }

    setCurrentPage(1);
    setQueryParam(newQueryParam);
  }, [debouncedSearchTerm]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  };
}
