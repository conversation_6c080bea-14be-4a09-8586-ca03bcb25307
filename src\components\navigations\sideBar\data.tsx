import React from 'react';
import { Paths } from '../data';
import {
  HandCoins,
  Pill,
  Stethoscope,
  CalendarPlus2,
  Gem,
  ReceiptText,
  Hospital,
  ClipboardPenLine,
  MessageCircleMore,
  Users,
  Home,
  Percent,
  Wallet,
  ArrowRightLeft,
  MapPin,
  UserRound,
  ClipboardList,
  BarChart4,
  Building2,
  UserCog,
  MessageSquare,
  FileSpreadsheet,
  Settings,
  MessagesSquare,
  Coffee,
  Lightbulb,
  BookOpen,
  Shield,
  Bot,
  Package,
  BarChart3,
  ShoppingCart,
  FileText,
  Truck,
  Mailbox,
} from 'lucide-react';

interface SidebarItem {
  label: string;
  url: string;
  icon: React.ElementType;
}

interface SidebarSection {
  name: string;
  children: SidebarItem[];
}

export const LeftSidebarData: SidebarSection[] = [
  {
    name: 'OVERVIEW',
    children: [
      {
        label: 'Dashboard',
        url: Paths.Dashboard,
        icon: Home,
      },
      {
        label: 'Packages',
        url: Paths.Packages,
        icon: HandCoins,
      },
      {
        label: 'Cafeteria',
        url: Paths.Cafeteria,
        icon: Coffee,
      },
      {
        label: 'Feedbacks',
        url: Paths.Feedbacks,
        icon: MessageCircleMore,
      },
      {
        label: 'Referrals',
        url: Paths.Referral,
        icon: ArrowRightLeft,
      },
      {
        label: 'Suggestion Box',
        url: Paths.SuggestionBox,
        icon: Mailbox,
      },
      // {
      //   label: 'Incident Report',
      //   url: Paths.Reporting,
      //   icon: ClipboardPenLine,
      // },
      // {
      //   label: 'Medications',
      //   url: Paths.Medications,
      //   icon: Pill,
      //   roles: ['superadmin', 'admin'],
      // },
      // {
      //   label: 'Appointments',
      //   url: Paths.Appointments,
      //   icon: CalendarPlus2,
      //   roles: ['superadmin', 'admin'],
      // },
    ],
  },
  {
    name: 'FINANCE',
    children: [
      // {
      //   label: 'Bill Requests',
      //   url: Paths.BillRequest,
      //   icon: ReceiptText,
      //   roles: ['superadmin', 'admin'],
      // },
      {
        label: 'Discounts',
        url: Paths.Discounts,
        icon: Percent,
      },
      {
        label: 'Transactions',
        url: Paths.Transactions,
        icon: Wallet,
      },
    ],
  },
  // {
  //   name: 'PATIENT MANAGEMENT',
  //   children: [
  //     {
  //       label: 'Patients',
  //       url: Paths.Patients,
  //       icon: UserRound,
  //     },
  //     {
  //       label: 'Appointments',
  //       url: Paths.Appointments,
  //       icon: CalendarPlus2,
  //     },
  // {
  //   label: 'Medical Records',
  //   url: Paths.MedicalRecords,
  //   icon: ClipboardList,
  // },
  // {
  //   label: 'Departments',
  //   url: Paths.Departments,
  //   icon: Building2,
  // },
  // {
  //   label: 'Doctors',
  //   url: Paths.Doctors,
  //   icon: UserCog,
  // },
  // {
  //   label: 'Interactions',
  //   url: Paths.Interactions,
  //   icon: MessageSquare,
  // },
  // {
  //   label: 'Analytics',
  //   url: Paths.HospitalAnalytics,
  //   icon: BarChart4,
  // },
  // {
  //   label: 'Data Analysis',
  //   url: Paths.DataAnalysis,
  //   icon: FileSpreadsheet,
  // },
  //   ],
  // },
  {
    name: 'MANAGE',
    children: [
      {
        label: 'Rewards',
        url: Paths.Rewards,
        icon: Gem,
      },
      {
        label: 'Locations',
        url: Paths.Location,
        icon: MapPin,
      },
    ],
  },
  // {
  //   name: 'VMI (VENDOR MANAGED INVENTORY)',
  //   children: [
  //     {
  //       label: 'Overview',
  //       url: Paths.VMIOverview,
  //       icon: BarChart3,
  //     },
  //     {
  //       label: 'Products',
  //       url: Paths.VMIProducts,
  //       icon: Package,
  //     },
  //     {
  //       label: 'Inventory',
  //       url: Paths.VMIInventory,
  //       icon: Truck,
  //     },
  //     {
  //       label: 'Requests',
  //       url: Paths.VMIRequests,
  //       icon: ShoppingCart,
  //     },
  //     {
  //       label: 'Invoices',
  //       url: Paths.VMIInvoices,
  //       icon: FileText,
  //     },
  //   ],
  // },
];

export const LeftSidebarFooterData: SidebarItem[] = [
  // {
  //   label: 'Forum',
  //   url: Paths.Forum,
  //   icon: MessagesSquare,
  // },
  {
    label: 'Innovation Hub',
    url: Paths.InnovationHub,
    icon: Lightbulb,
  },
  {
    label: 'Process Library',
    url: Paths.ProcessDictionary,
    icon: BookOpen,
  },
  // {
  //   label: 'CHIS',
  //   url: Paths.CHIS,
  //   icon: Shield,
  // },
  // {
  //   label: 'AI Assistant',
  //   url: Paths.AIAssistant,
  //   icon: Bot,
  // },
];
