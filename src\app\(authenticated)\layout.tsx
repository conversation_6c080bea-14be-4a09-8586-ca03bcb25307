import Layout from '@/components/common/layout';
import { NotificationPermission } from '@/components/notification-permission';
import { PWAInstallPrompt } from '@/components/auth/pwa-install-prompt';
import WebSocketProvider from '@/Providers/WebSocketProvider';
import { SidebarProvider } from '@/contexts/SidebarContext';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <WebSocketProvider>
      <SidebarProvider>
        <Layout>
          <main>{children}</main>
          <NotificationPermission />
          <PWAInstallPrompt />
        </Layout>
      </SidebarProvider>
    </WebSocketProvider>
  );
}
