'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export default function StepAdditionalInfo() {
  const { register, watch, setValue } = useFormContext();
  const ccst = watch('additional.ccst');

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Date of last medical appraisal
        </label>
        <Input type="date" {...register('additional.lastAppraisal')} />
      </div>
      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Confirmation of your Responsible Officer’s name and contact details
        </label>
        <Input {...register('additional.responsibleOfficer')} />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Have you been awarded a CCST or CCT?
        </label>
        <Select onValueChange={(value) => setValue('additional.ccst', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Yes">Yes</SelectItem>
            <SelectItem value="No">No</SelectItem>
          </SelectContent>
        </Select>
        {ccst === 'Yes' && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Date of Award
            </label>
            <Input type="date" {...register('additional.ccstDate')} />
          </div>
        )}
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-foreground">
          Do you meet the CME requirements of your Postgraduate College of MDCN?
        </label>
        <Select onValueChange={(value) => setValue('additional.cme', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Yes">Yes</SelectItem>
            <SelectItem value="No">No</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-4">
        <h3 className="font-semibold text-foreground border-b pb-2">
          Please give names of any other private hospitals where you have applied for or been granted/refused practising privileges (including other CEDARCREST / BMI Hospitals)
        </h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Granted Practising Privileges:
            </label>
            <textarea
              {...register('additional.hospitalsGranted')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Refused Practising Privileges:
            </label>
            <textarea
              {...register('additional.hospitalsRefused')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Scope of Practice / Qualification Details
        </h3>
        <p className='text-xs'>Please give a detailed description of the scope of your normal practice below. Please attach
          a list of those procedures, techniques or treatments you intend to undertake within this
          scope, at this hospital, to this application form including the age band of the patients you
          will treat for each procedure.
          (Please note: failure to identify procedures you may wish to undertake will put you in breach of the
          Practising Privileges Policy, if in doubt check with the Executive Director)</p>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Areas of sub-specialties
            </label>
            <textarea
              {...register('additional.scope')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <h3>Within your scope of practice, please provide further details of any sub-specialties, or
              additional training that you have received – e.g. Minimal invasive techniques that you
              intend to use in your practice:</h3>
            <label className="text-sm font-medium text-foreground">
              Speciality
            </label>
            <Input
              {...register('additional.specialty')}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Sub-Speciality(ies)
            </label>
            <textarea
              {...register('additional.subSpecialty')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Full Breakdown
            </label>
            <p className='text-sm text-muted-foreground'>Give a Numbered Breakdown in this format: <br />
              Declared Procedure - Training Date(dd/mm/yy) - Trained by - Certificate Issued By - Date of Issue(dd/mm/yy)
            </p>
            <textarea
              placeholder='1. Basic Life Support (BLS) - 15/07/25 - Dr. Sarah Ahmed, Certified AHA Instructor - American Heart Association (AHA) - 15/07/25'
              {...register('additional.breakDown')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Please list any other professional organisations with which you are registered:

            </label>
            <textarea
              {...register('additional.organisations')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
