import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Group, SquarePen, Search } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { GetDepartmentList } from '@/api/reward/data';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import {
  TooltipProvider,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
// import Details from '../details';
import DepartmentForm from './form';

const Department = () => {
  const [open, setOpen] = useState(false);
  const { searchTerm, handleSearchChange, queryParam } =
    useSearchAndPagination();

  const [detail, setDetail] = useState<any | null>(null);

  const { department, departmentLoading, mutate } = GetDepartmentList(
    `?${queryParam ? `${queryParam}` : ''}`
  );

  const departmentData = department?.data;

  const handleEventFromModal = (staff: any) => {
    setDetail(staff);
    setOpen(true);
  };

  return (
    <>
      <TooltipProvider>
        <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
          <Badge className="h-7 cursor-pointer" onClick={() => setOpen(true)}>
            Add Department
          </Badge>
          <div className="flex flex-wrap justify-between items-center mb-4 p-4">
            {/* <div className="flex flex-wrap gap-3 items-center"> */}
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search department..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            {/* </div> */}
          </div>
          <table className="w-full table-auto text-left text-xs">
            <thead className="bg-primary text-gray-100 dark:text-black">
              <tr>
                <th className="table-style">S/N</th>
                <th className="table-style">Department</th>
                <th className="table-style">Manager</th>
                <th className="table-style">Units</th>
                <th className="table-style">Action</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
              {departmentData?.map((dept: any, index: any) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={dept.id}
                >
                  <td className="table-style">{index + 1}</td>
                  <td className="table-style">{dept.name}</td>
                  <td className="table-style">
                    {dept.manager ? dept.manager.fullName : 'N/A'}
                  </td>
                  <td className="table-style">{dept?.units?.length}</td>
                  <td className="table-style flex gap-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SquarePen
                          onClick={() => handleEventFromModal(dept)}
                          className="w-4 h-4 cursor-pointer"
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit Department</p>
                      </TooltipContent>
                    </Tooltip>
                    {/* <Tooltip>
                      <TooltipTrigger asChild>
                        <Group
                          onClick={() => handleEventFromModal(dept)}
                          className="w-4 h-4 cursor-pointer"
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>View Units</p>
                      </TooltipContent>
                    </Tooltip> */}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {departmentLoading ? (
            <LoadingState />
          ) : departmentData?.length === 0 ? (
            <EmptyState />
          ) : null}
        </div>
      </TooltipProvider>
      <DepartmentForm
        open={open}
        setOpen={setOpen}
        mutate={mutate}
        isEditMode={false}
      />
      {/* Form for EDITING an existing department */}
      {detail && (
        <DepartmentForm
          open={open}
          setOpen={setOpen}
          mutate={mutate}
          isEditMode={true}
          initialData={detail}
        />
      )}
    </>
  );
};

export default Department;
