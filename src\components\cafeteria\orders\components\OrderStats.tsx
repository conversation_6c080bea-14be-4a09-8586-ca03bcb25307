'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { currencyFormat } from '@/lib/utils';
import dayjs from 'dayjs';

interface OrderStatsProps {
  stats: any;
  orderStatsLoading: boolean;
  viewType: 'my' | 'all';
  startDate?: Date;
  endDate?: Date;
}

export default function OrderStats({
  stats,
  orderStatsLoading,
  viewType,
  startDate,
  endDate,
}: OrderStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Overall Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {orderStatsLoading ? (
              <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-16 rounded"></div>
            ) : (
              stats?.overallOrders || 0
            )}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-amber-500">
            {orderStatsLoading ? (
              <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-16 rounded"></div>
            ) : (
              stats?.totalOrders || 0
            )}
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Amount
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-500">
            {orderStatsLoading ? (
              <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-20 rounded"></div>
            ) : (
              <>
                {currencyFormat(stats?.totalAmount)}
                <p className="text-xs text-gray-500">
                  Unpaid Credit{' '}
                  <span>{currencyFormat(stats?.unpaidCredit)}</span>
                </p>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
