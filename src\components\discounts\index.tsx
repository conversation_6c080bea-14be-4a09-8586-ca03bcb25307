'use client';

import { CirclePercent, Percent } from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { Badge } from '../ui/badge';
import Link from 'next/link';
import DiscountRecord from './components/data-table';

export default function DiscountPage() {
  const canViewrReward = hasPermission(PERMISSIONS.REWARD_VIEW);
  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <CirclePercent className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Discount Usage Management
      </h2>
      <div className="flex gap-2">
        {canViewrReward && (
          <Link href={'/discounts/discount-code'}>
            <Badge className="cursor-pointer py-1.5 px-3">Discount Codes</Badge>
          </Link>
        )}
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        <DiscountRecord />
      </div>
    </>
  );
}
