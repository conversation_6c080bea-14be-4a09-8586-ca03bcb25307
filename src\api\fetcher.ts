import axios from 'axios';
import { toast } from 'sonner';
import { snapshot } from 'valtio';
import { accessTokenStore } from '@/store/accessToken';
import { Logout } from '@/lib/utils';

// Network detection utility
const isOnline = () => {
  return typeof navigator !== 'undefined' ? navigator.onLine : true;
};

// Check if error is network-related
const isNetworkError = (error: any) => {
  return (
    (typeof navigator !== 'undefined' && !navigator.onLine) ||
    error.code === 'NETWORK_ERROR' ||
    error.code === 'ECONNABORTED' ||
    error.message === 'Network Error' ||
    (error.response === undefined && error.request !== undefined)
  );
};

interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: Record<string, any>;
  request?: any;
}

interface ApiError {
  response?: ApiResponse;
  message: string;
  config: Record<string, any>;
  code?: string;
  request?: any;
}

export const api = process.env.NEXT_PUBLIC_CEDARCREST_API_V1;

export const myApi = axios.create({
  baseURL: api,
  headers: { 'Content-Type': 'application/json' },
});

myApi.interceptors.request.use(
  (config) => {
    // Check network connectivity before making request
    if (!isOnline()) {
      return Promise.reject({
        message: 'No internet connection',
        code: 'NETWORK_ERROR',
        isNetworkError: true,
      });
    }

    const accessToken = snapshot(accessTokenStore).accessToken;
    const newConfig = { ...config };
    newConfig.headers = newConfig.headers || {};

    if (accessToken) {
      newConfig.headers.Authorization = `Bearer ${accessToken}`;
    }

    return newConfig;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor
myApi.interceptors.response.use(
  function (response) {
    return response;
  },
  async function (error) {
    // Handle network errors first
    if (isNetworkError(error) || error.isNetworkError) {
      // Don't show toast for network errors to avoid spam
      console.warn('Network error detected, skipping API call');
      return Promise.reject({
        ...error,
        isNetworkError: true,
        message: 'No internet connection',
      });
    }

    if (!axios.isAxiosError(error) || !error.response) {
      toast.error('Something went wrong. Please try again.');
      return Promise.reject(error);
    }

    const status = error.response.status;
    const message =
      error.response.data?.message || 'Unexpected error occurred.';

    if (status === 400) {
      toast.error(message);
      return Promise.reject(error.response.data);
    }

    if (status === 401) {
      // Special handling for GetProfile API errors
      const isProfileRequest = error.config?.url?.includes('/staff/profile');
      if (isProfileRequest) {
        console.error('GetProfile API returned 401, logging out user');
        Logout();
        return Promise.reject('Profile authentication failed');
      }

      Logout();
      return Promise.reject('Unauthorized');
    }

    if (status === 404) {
      toast.error(message);
      return Promise.reject(error.response.data);
    }

    if (status === 403) {
      toast.warning(message);
      return Promise.reject(
        "Sorry! You're not authorized to perform this action"
      );
    }

    toast.error('Unexpected server error.');
    return Promise.reject(error);
  }
);

export const fetcher = async <T = any>(url: string): Promise<T> => {
  const accessToken = snapshot(accessTokenStore).accessToken;

  if (!accessToken) {
    throw new Error('No access token available');
  }

  // Check network connectivity before making request
  if (!isOnline()) {
    throw {
      message: 'No internet connection',
      code: 'NETWORK_ERROR',
      isNetworkError: true,
    };
  }

  try {
    const response: ApiResponse<T> = await myApi.get(url, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    return response.data;
  } catch (error) {
    // Mark network errors
    if (isNetworkError(error)) {
      throw {
        ...(typeof error === 'object' && error !== null ? error : {}),
        isNetworkError: true,
        message: 'No internet connection',
      };
    }
    throw error;
  }
};
