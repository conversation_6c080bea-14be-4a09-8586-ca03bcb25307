'use client';

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  notificationEmailsSchema,
  EmailFormValues,
  NotificationEmailsFormValues,
} from '@/components/validations/systemSettings';
import { Form } from '@/components/ui/form';
import { InputField } from '@/components/common/form';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Save, Mail, AlertCircle } from 'lucide-react';
import { GetNotificationEmails } from '@/api/settings/data';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface NotificationEmailsSettingsProps {
  canEdit: boolean;
}

export default function NotificationEmailsSettings({
  canEdit,
}: NotificationEmailsSettingsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { notificationEmails, notificationEmailsLoading, mutate } =
    GetNotificationEmails();

  const form = useForm<NotificationEmailsFormValues>({
    resolver: zodResolver(notificationEmailsSchema),
    defaultValues: {
      emails: [{ email: '' }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'emails',
  });

  useEffect(() => {
    if (notificationEmails?.data && notificationEmails.data.length > 0) {
      // Transform the emails array to match the form structure
      const formattedEmails = notificationEmails.data.map((email: string) => ({
        email,
      }));

      form.reset({ emails: formattedEmails });
    }
  }, [notificationEmails, form]);

  const onSubmit = async (data: NotificationEmailsFormValues) => {
    if (!canEdit) {
      toast.error('You do not have permission to update notification emails');
      return;
    }

    try {
      setIsLoading(true);
      // Extract just the email strings from the form data
      const emailsArray = data.emails.map((item) => item.email);

      const res = await myApi.post('/settings/admin-notification-emails', {
        emails: emailsArray,
      });

      if (res.status === 200) {
        toast.success(
          res.data.message || 'Notification emails updated successfully'
        );
        mutate();
      }
    } catch (error: any) {
      toast.error(
        error?.response?.data?.message ||
          'An error occurred while updating notification emails'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const addEmail = () => {
    append({ email: '' });
  };

  if (notificationEmailsLoading) {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {fields.length === 0 && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No notification emails configured. Add at least one email
                address.
              </AlertDescription>
            </Alert>
          )}

          {fields.map((field, index) => (
            <div key={field.id} className="flex items-center gap-2">
              <div className="flex-1">
                <InputField
                  control={form.control}
                  name={`emails.${index}.email`}
                  label={index === 0 ? 'Email Address' : ''}
                  placeholder="<EMAIL>"
                  type="email"
                  disabled={!canEdit}
                />
              </div>
              {canEdit && fields.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="mt-6"
                  onClick={() => remove(index)}
                >
                  <Trash2 className="h-4 w-4 text-red-500" />
                </Button>
              )}
            </div>
          ))}

          {canEdit && (
            <div className="flex flex-col sm:flex-row gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addEmail}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                Add Email
              </Button>

              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center gap-1"
                size="sm"
              >
                {isLoading ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-white"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                Save Changes
              </Button>
            </div>
          )}
        </form>
      </Form>

      <div className="border-t border-gray-200 dark:border-gray-800 pt-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-1">
          <Mail className="h-4 w-4" />
          How It Works
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          When a package booking is completed, a notification email will be sent
          to all the email addresses listed above. The email will contain
          details about the booking, including the package name, customer
          information, and payment details.
        </p>
      </div>
    </div>
  );
}
