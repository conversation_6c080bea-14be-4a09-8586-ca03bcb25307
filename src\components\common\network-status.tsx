'use client';

import { useEffect, useState } from 'react';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { toast } from 'sonner';
import { WifiOff, Wifi } from 'lucide-react';

export function NetworkStatusIndicator() {
  const { isOnline, wasOffline } = useNetworkStatus();
  const [showOfflineToast, setShowOfflineToast] = useState(false);

  useEffect(() => {
    if (!isOnline && !showOfflineToast) {
      toast.error('No internet connection. Some features may not work.', {
        id: 'network-offline',
        duration: Infinity,
        icon: <WifiOff className="h-4 w-4" />,
      });
      setShowOfflineToast(true);
    }

    if (isOnline && wasOffline && showOfflineToast) {
      toast.dismiss('network-offline');
      toast.success('Internet connection restored', {
        id: 'network-online',
        duration: 3000,
        icon: <Wifi className="h-4 w-4" />,
      });
      setShowOfflineToast(false);
    }
  }, [isOnline, wasOffline, showOfflineToast]);

  // Don't render anything - we use toasts for notifications
  return null;
}

export function NetworkStatusBanner() {
  const { isOnline } = useNetworkStatus();

  if (isOnline) return null;

  return (
    <div className="bg-red-600 text-white px-4 py-2 text-center text-sm font-medium">
      <div className="flex items-center justify-center gap-2">
        <WifiOff className="h-4 w-4" />
        <span>No internet connection - Working in offline mode</span>
      </div>
    </div>
  );
}
